namespace EasyPeasyFirstPersonController
{
    using System;
    using System.Collections;
    using UnityEngine;

    public class FirstPersonController : MonoBehaviour
    {
        [Range(0, 100)] public float mouseSensitivity = 25f;
        [Range(0f, 200f)] private float snappiness = 100f;
        [Range(0f, 20f)] public float walkSpeed = 10f;
        [Range(0f, 30f)] public float sprintSpeed = 15f;
        [Range(0f, 10f)] public float crouchSpeed = 6f;
        public float crouchHeight = 1f;
        public float crouchCameraHeight = 0.5f;
        public float slideSpeed = 9f;
        public float slideDuration = 0.7f;
        public float slideFovBoost = 5f;
        public float slideTiltAngle = 5f;

        [Header("Audio Settings")]
        public AudioClip slideSound;
        [Range(0f, 1f)] public float slideSoundVolume = 0.5f;

        public AudioClip dashSound;
        [Range(0f, 1f)] public float dashSoundVolume = 0.7f;

        public AudioClip jumpSound;
        [Range(0f, 1f)] public float jumpSoundVolume = 0.6f;

        public AudioClip[] footstepSounds;
        [Range(0f, 1f)] public float footstepVolume = 0.4f;
        [Range(0.1f, 2f)] public float footstepInterval = 0.5f;

        [Header("Groundpound Settings")]
        public AudioClip groundpoundSound;
        [Range(0f, 1f)] public float groundpoundSoundVolume = 0.8f;
        [Range(5f, 50f)] public float groundpoundForce = 25f;
        [Range(1f, 10f)] public float groundpoundDamageRadius = 5f;
        [Range(10f, 100f)] public float groundpoundDamage = 50f;
        public LayerMask enemyLayerMask = -1;
        [Range(0f, 15f)] public float jumpSpeed = 3f;
        [Range(0f, 50f)] public float gravity = 9.81f;
        public bool coyoteTimeEnabled = true;
        public float coyoteTimeDuration = 0.25f;

        [Header("Dash Settings")]
        public bool canDash = true;
        [Range(5f, 30f)] public float dashForce = 15f;
        [Range(0.1f, 1f)] public float dashDuration = 0.2f;
        [Range(1f, 10f)] public float dashRechargeTime = 3f;
        [Range(1, 5)] public int maxDashCharges = 3;
        public float dashFovBoost = 10f;
        public float normalFov = 60f;
        public float sprintFov = 70f;
        public float fovChangeSpeed = 5f;
        public float walkingBobbingSpeed = 14f;
        public float bobbingAmount = 0.05f;
        private float sprintBobMultiplier = 1.2f;
        private float recoilReturnSpeed = 8f;
        public bool canSlide = true;
        public bool canJump = true;
        public bool canSprint = true;
        public bool canCrouch = true;
        public Transform groundCheck;
        public float groundDistance = 0.3f;
        public LayerMask groundMask;
        public Transform playerCamera;
        public Transform cameraParent;
        private float rotX, rotY;
        private float xVelocity, yVelocity;
        private CharacterController characterController;
        private Vector3 moveDirection = Vector3.zero;
        private bool isGrounded;
        private Vector2 moveInput;
        public bool isSprinting;
        public bool isCrouching;
        public bool isSliding;
        public bool isDashing;
        public bool isGroundpounding;
        private float slideTimer;
        private float postSlideCrouchTimer;
        private Vector3 slideDirection;
        private float dashTimer;
        private Vector3 dashDirection;
        private int currentDashCharges;
        private float dashRechargeTimer;
        private float originalHeight;
        private float originalCameraParentHeight;
        private float coyoteTimer;
        private Camera cam;
        private AudioSource slideAudioSource;
        private AudioSource dashAudioSource;
        private AudioSource jumpAudioSource;
        private AudioSource footstepAudioSource;
        private AudioSource groundpoundAudioSource;
        private float bobTimer;
        private float footstepTimer;
        private float defaultPosY;
        private Vector3 recoil = Vector3.zero;
        private bool isLook = true, isMove = true;
        private float currentCameraHeight;
        private float currentBobOffset;
        private float currentFov;
        private float fovVelocity;
        private float currentSlideSpeed;
        private float slideSpeedVelocity;
        private float currentTiltAngle;
        private float tiltVelocity;
        private Component playerHealthComponent;

        public float CurrentCameraHeight => isCrouching || isSliding ? crouchCameraHeight : originalCameraParentHeight;
        public int CurrentDashCharges => currentDashCharges;
        public float DashRechargeProgress => dashRechargeTimer / dashRechargeTime;
        public bool IsDashInvulnerable => isDashing;

        private void Awake()
        {
            characterController = GetComponent<CharacterController>();
            cam = playerCamera.GetComponent<Camera>();
            originalHeight = characterController.height;
            originalCameraParentHeight = cameraParent.localPosition.y;
            defaultPosY = cameraParent.localPosition.y;
            // Initialize slide audio
            slideAudioSource = gameObject.AddComponent<AudioSource>();
            slideAudioSource.playOnAwake = false;
            slideAudioSource.loop = false;
            slideAudioSource.volume = slideSoundVolume;
            slideAudioSource.clip = slideSound;

            // Initialize dash audio
            dashAudioSource = gameObject.AddComponent<AudioSource>();
            dashAudioSource.playOnAwake = false;
            dashAudioSource.loop = false;
            dashAudioSource.volume = dashSoundVolume;
            dashAudioSource.clip = dashSound;

            // Initialize jump audio
            jumpAudioSource = gameObject.AddComponent<AudioSource>();
            jumpAudioSource.playOnAwake = false;
            jumpAudioSource.loop = false;
            jumpAudioSource.volume = jumpSoundVolume;
            jumpAudioSource.clip = jumpSound;

            // Initialize footstep audio
            footstepAudioSource = gameObject.AddComponent<AudioSource>();
            footstepAudioSource.playOnAwake = false;
            footstepAudioSource.loop = false;
            footstepAudioSource.volume = footstepVolume;

            // Initialize groundpound audio
            groundpoundAudioSource = gameObject.AddComponent<AudioSource>();
            groundpoundAudioSource.playOnAwake = false;
            groundpoundAudioSource.loop = false;
            groundpoundAudioSource.volume = groundpoundSoundVolume;
            groundpoundAudioSource.clip = groundpoundSound;
            Cursor.lockState = CursorLockMode.Locked;
            currentCameraHeight = originalCameraParentHeight;
            currentBobOffset = 0f;
            currentFov = normalFov;
            currentSlideSpeed = 0f;
            currentTiltAngle = 0f;
            currentDashCharges = maxDashCharges;
            dashRechargeTimer = 0f;
            playerHealthComponent = GetComponent("PlayerHealth");
        }

        private void OnDisable()
        {
            // Ensure dash invulnerability is restored if component is disabled
            if (isDashing && playerHealthComponent != null)
            {
                playerHealthComponent.SendMessage("EnableDamage", SendMessageOptions.DontRequireReceiver);
                isDashing = false;
            }

            // Stop slide sound if playing
            if (isSliding && slideAudioSource != null && slideAudioSource.isPlaying)
            {
                slideAudioSource.Stop();
            }
        }

        private void Update()
        {
            isGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);
            if (isGrounded && moveDirection.y < 0)
            {
                moveDirection.y = -2f;
                coyoteTimer = coyoteTimeEnabled ? coyoteTimeDuration : 0f;
            }
            else if (coyoteTimeEnabled)
            {
                coyoteTimer -= Time.deltaTime;
            }

            if (isLook)
            {
                float mouseX = Input.GetAxis("Mouse X") * 10 * mouseSensitivity * Time.deltaTime;
                float mouseY = Input.GetAxis("Mouse Y") * 10 * mouseSensitivity * Time.deltaTime;

                rotX += mouseX;
                rotY -= mouseY;
                rotY = Mathf.Clamp(rotY, -90f, 90f);

                xVelocity = Mathf.Lerp(xVelocity, rotX, snappiness * Time.deltaTime);
                yVelocity = Mathf.Lerp(yVelocity, rotY, snappiness * Time.deltaTime);

                float targetTiltAngle = isSliding ? slideTiltAngle : 0f;
                currentTiltAngle = Mathf.SmoothDamp(currentTiltAngle, targetTiltAngle, ref tiltVelocity, 0.2f);
                playerCamera.transform.localRotation = Quaternion.Euler(yVelocity - currentTiltAngle, 0f, 0f);
                transform.rotation = Quaternion.Euler(0f, xVelocity, 0f);
            }

            HandleHeadBob();
            HandleDash();
            HandleGroundpound();
            HandleFootsteps();
            UpdateAllAudio();

            bool wantsToCrouch = canCrouch && Input.GetKey(KeyCode.LeftControl) && !isSliding && !isGroundpounding;
            Vector3 point1 = transform.position + characterController.center - Vector3.up * (characterController.height * 0.5f);
            Vector3 point2 = point1 + Vector3.up * characterController.height * 0.6f;
            float capsuleRadius = characterController.radius * 0.95f;
            float castDistance = isSliding ? originalHeight + 0.2f : originalHeight - crouchHeight + 0.2f;
            bool hasCeiling = Physics.CapsuleCast(point1, point2, capsuleRadius, Vector3.up, castDistance, groundMask);
            if (isSliding)
            {
                postSlideCrouchTimer = 0.3f;
            }
            if (postSlideCrouchTimer > 0)
            {
                postSlideCrouchTimer -= Time.deltaTime;
                isCrouching = canCrouch;
            }
            else
            {
                isCrouching = canCrouch && (wantsToCrouch || (hasCeiling && !isSliding));
            }

            if (canSlide && isSprinting && Input.GetKeyDown(KeyCode.LeftControl) && isGrounded)
            {
                isSliding = true;
                slideTimer = slideDuration;
                slideDirection = moveInput.magnitude > 0.1f ? (transform.right * moveInput.x + transform.forward * moveInput.y).normalized : transform.forward;
                currentSlideSpeed = sprintSpeed;

                // Play slide sound
                if (slideSound != null && slideAudioSource != null)
                {
                    slideAudioSource.clip = slideSound;
                    slideAudioSource.volume = slideSoundVolume;
                    slideAudioSource.Play();
                }
            }

            float slideProgress = slideTimer / slideDuration;
            if (isSliding)
            {
                slideTimer -= Time.deltaTime;
                if (slideTimer <= 0f || !isGrounded)
                {
                    StopSliding();
                }
                else
                {
                    float targetSlideSpeed = slideSpeed * Mathf.Lerp(0.7f, 1f, slideProgress);
                    currentSlideSpeed = Mathf.SmoothDamp(currentSlideSpeed, targetSlideSpeed, ref slideSpeedVelocity, 0.2f);
                    characterController.Move(slideDirection * currentSlideSpeed * Time.deltaTime);
                }
            }

            float targetHeight = isCrouching || isSliding ? crouchHeight : originalHeight;
            characterController.height = Mathf.Lerp(characterController.height, targetHeight, Time.deltaTime * 10f);
            characterController.center = new Vector3(0f, characterController.height * 0.5f, 0f);

            float targetFov = isSprinting ? sprintFov : (isSliding ? sprintFov + (slideFovBoost * Mathf.Lerp(0f, 1f, 1f - slideProgress)) : (isDashing ? normalFov + dashFovBoost : normalFov));
            currentFov = Mathf.SmoothDamp(currentFov, targetFov, ref fovVelocity, 1f / fovChangeSpeed);
            cam.fieldOfView = currentFov;

            HandleMovement();
        }

        private void HandleHeadBob()
        {
            Vector3 horizontalVelocity = new Vector3(characterController.velocity.x, 0f, characterController.velocity.z);
            bool isMovingEnough = horizontalVelocity.magnitude > 0.1f;

            float targetBobOffset = isMovingEnough ? Mathf.Sin(bobTimer) * bobbingAmount : 0f;
            currentBobOffset = Mathf.Lerp(currentBobOffset, targetBobOffset, Time.deltaTime * walkingBobbingSpeed);

            if (!isGrounded || isSliding || isCrouching || isDashing || isGroundpounding)
            {
                bobTimer = 0f;
                float targetCameraHeight = isCrouching || isSliding ? crouchCameraHeight : originalCameraParentHeight;
                currentCameraHeight = Mathf.Lerp(currentCameraHeight, targetCameraHeight, Time.deltaTime * 10f);
                cameraParent.localPosition = new Vector3(
                    cameraParent.localPosition.x,
                    currentCameraHeight + currentBobOffset,
                    cameraParent.localPosition.z);
                recoil = Vector3.zero;
                cameraParent.localRotation = Quaternion.RotateTowards(cameraParent.localRotation, Quaternion.Euler(recoil), recoilReturnSpeed * Time.deltaTime);
                return;
            }

            if (isMovingEnough)
            {
                float bobSpeed = walkingBobbingSpeed * (isSprinting ? sprintBobMultiplier : 1f);
                bobTimer += Time.deltaTime * bobSpeed;
                float targetCameraHeight = isCrouching || isSliding ? crouchCameraHeight : originalCameraParentHeight;
                currentCameraHeight = Mathf.Lerp(currentCameraHeight, targetCameraHeight, Time.deltaTime * 10f);
                cameraParent.localPosition = new Vector3(
                    cameraParent.localPosition.x,
                    currentCameraHeight + currentBobOffset,
                    cameraParent.localPosition.z);
                recoil.z = moveInput.x * -2f;
            }
            else
            {
                bobTimer = 0f;
                float targetCameraHeight = isCrouching || isSliding ? crouchCameraHeight : originalCameraParentHeight;
                currentCameraHeight = Mathf.Lerp(currentCameraHeight, targetCameraHeight, Time.deltaTime * 10f);
                cameraParent.localPosition = new Vector3(
                    cameraParent.localPosition.x,
                    currentCameraHeight + currentBobOffset,
                    cameraParent.localPosition.z);
                recoil = Vector3.zero;
            }

            cameraParent.localRotation = Quaternion.RotateTowards(cameraParent.localRotation, Quaternion.Euler(recoil), recoilReturnSpeed * Time.deltaTime);
        }

        private void HandleMovement()
        {
            moveInput.x = Input.GetAxis("Horizontal");
            moveInput.y = Input.GetAxis("Vertical");
            isSprinting = canSprint && Input.GetKey(KeyCode.LeftShift) && moveInput.y > 0.1f && isGrounded && !isCrouching && !isSliding && !isDashing && !isGroundpounding;

            float currentSpeed = isCrouching ? crouchSpeed : (isSprinting ? sprintSpeed : walkSpeed);
            if (!isMove || isDashing || isGroundpounding) currentSpeed = 0f;

            Vector3 direction = new Vector3(moveInput.x, 0f, moveInput.y);
            Vector3 moveVector = transform.TransformDirection(direction) * currentSpeed;
            moveVector = Vector3.ClampMagnitude(moveVector, currentSpeed);

            if (isGrounded || coyoteTimer > 0f)
            {
                if (canJump && Input.GetKeyDown(KeyCode.Space) && !isSliding && !isDashing && !isGroundpounding)
                {
                    moveDirection.y = jumpSpeed;

                    // Play jump sound
                    if (jumpSound != null && jumpAudioSource != null)
                    {
                        jumpAudioSource.clip = jumpSound;
                        jumpAudioSource.volume = jumpSoundVolume;
                        jumpAudioSource.Play();
                    }
                }
                else if (moveDirection.y < 0)
                {
                    moveDirection.y = -2f;
                }
            }
            else
            {
                moveDirection.y -= gravity * Time.deltaTime;
            }

            if (!isSliding && !isDashing && !isGroundpounding)
            {
                moveDirection = new Vector3(moveVector.x, moveDirection.y, moveVector.z);
                characterController.Move(moveDirection * Time.deltaTime);
            }
            else if (isGroundpounding)
            {
                // During groundpound, only apply the downward movement
                characterController.Move(new Vector3(0, moveDirection.y, 0) * Time.deltaTime);
            }
        }

        private void HandleDash()
        {
            // Handle dash recharge
            if (currentDashCharges < maxDashCharges)
            {
                dashRechargeTimer += Time.deltaTime;
                if (dashRechargeTimer >= dashRechargeTime)
                {
                    currentDashCharges++;
                    dashRechargeTimer = 0f;
                }
            }

            // Handle dash input
            if (canDash && Input.GetKeyDown(KeyCode.Q) && currentDashCharges > 0 && !isSliding && !isDashing)
            {
                StartDash();
            }

            // Handle active dash
            if (isDashing)
            {
                dashTimer -= Time.deltaTime;
                if (dashTimer <= 0f)
                {
                    isDashing = false;
                    // End dash invulnerability
                    if (playerHealthComponent != null)
                    {
                        playerHealthComponent.SendMessage("EnableDamage", SendMessageOptions.DontRequireReceiver);
                    }
                }
                else
                {
                    // Apply dash movement
                    Vector3 dashMovement = dashDirection * dashForce * Time.deltaTime;
                    characterController.Move(dashMovement);
                }
            }
        }

        private void HandleGroundpound()
        {
            // Check for groundpound input: crouch while in air
            if (canCrouch && Input.GetKeyDown(KeyCode.LeftControl) && !isGrounded && !isSliding && !isDashing && !isGroundpounding)
            {
                StartGroundpound();
            }

            // Handle active groundpound
            if (isGroundpounding)
            {
                // Apply strong downward force
                moveDirection.y = -groundpoundForce;

                // Check if we hit the ground
                if (isGrounded)
                {
                    PerformGroundpoundImpact();
                    isGroundpounding = false;
                }
            }
        }

        private void StartGroundpound()
        {
            isGroundpounding = true;

            // Play groundpound sound
            if (groundpoundSound != null && groundpoundAudioSource != null)
            {
                groundpoundAudioSource.clip = groundpoundSound;
                groundpoundAudioSource.volume = groundpoundSoundVolume;
                groundpoundAudioSource.Play();
            }
        }

        private void PerformGroundpoundImpact()
        {
            // Find all enemies within damage radius
            Collider[] enemiesInRange = Physics.OverlapSphere(transform.position, groundpoundDamageRadius, enemyLayerMask);

            foreach (Collider enemy in enemiesInRange)
            {
                // Try to damage the enemy
                Component enemyHealth = enemy.GetComponent("EnemyHealth");
                if (enemyHealth == null)
                {
                    enemyHealth = enemy.GetComponent("Health");
                }

                if (enemyHealth != null)
                {
                    enemyHealth.SendMessage("TakeDamage", groundpoundDamage, SendMessageOptions.DontRequireReceiver);
                }

                // Apply knockback force if the enemy has a Rigidbody
                Rigidbody enemyRb = enemy.GetComponent<Rigidbody>();
                if (enemyRb != null)
                {
                    Vector3 knockbackDirection = (enemy.transform.position - transform.position).normalized;
                    knockbackDirection.y = 0.5f; // Add some upward force
                    enemyRb.AddForce(knockbackDirection * groundpoundForce * 2f, ForceMode.Impulse);
                }
            }
        }

        private void StartDash()
        {
            isDashing = true;
            dashTimer = dashDuration;
            currentDashCharges--;

            // Grant invulnerability during dash
            if (playerHealthComponent != null)
            {
                playerHealthComponent.SendMessage("DisableDamage", SendMessageOptions.DontRequireReceiver);
            }

            // Play dash sound
            if (dashSound != null && dashAudioSource != null)
            {
                dashAudioSource.clip = dashSound;
                dashAudioSource.volume = dashSoundVolume;
                dashAudioSource.Play();
            }

            // Determine dash direction based on input
            Vector2 inputDirection = new Vector2(Input.GetAxis("Horizontal"), Input.GetAxis("Vertical"));
            if (inputDirection.magnitude > 0.1f)
            {
                // Dash in movement direction
                dashDirection = (transform.right * inputDirection.x + transform.forward * inputDirection.y).normalized;
            }
            else
            {
                // Dash forward if no input
                dashDirection = transform.forward;
            }
        }

        private void StopSliding()
        {
            if (isSliding)
            {
                isSliding = false;
                // Stop slide sound when sliding ends
                if (slideAudioSource != null && slideAudioSource.isPlaying)
                {
                    slideAudioSource.Stop();
                }
            }
        }

        public void SetControl(bool newState)
        {
            SetLookControl(newState);
            SetMoveControl(newState);

            // If disabling controls, ensure dash invulnerability is restored
            if (!newState && isDashing && playerHealthComponent != null)
            {
                playerHealthComponent.SendMessage("EnableDamage", SendMessageOptions.DontRequireReceiver);
                isDashing = false;
            }
        }

        public void SetLookControl(bool newState)
        {
            isLook = newState;
        }

        public void SetMoveControl(bool newState)
        {
            isMove = newState;
        }

        public void SetCursorVisibility(bool newVisibility)
        {
            Cursor.lockState = newVisibility ? CursorLockMode.None : CursorLockMode.Locked;
            Cursor.visible = newVisibility;
        }

        private void HandleFootsteps()
        {
            // Only play footsteps when grounded, moving, and not sliding/dashing/groundpounding
            if (isGrounded && !isSliding && !isDashing && !isGroundpounding && footstepSounds != null && footstepSounds.Length > 0)
            {
                Vector3 horizontalVelocity = new Vector3(characterController.velocity.x, 0f, characterController.velocity.z);
                bool isMovingEnough = horizontalVelocity.magnitude > 0.5f;

                if (isMovingEnough)
                {
                    footstepTimer += Time.deltaTime;
                    float currentFootstepInterval = footstepInterval / (isSprinting ? 1.5f : 1f); // Faster footsteps when sprinting

                    if (footstepTimer >= currentFootstepInterval)
                    {
                        footstepTimer = 0f;
                        PlayRandomFootstep();
                    }
                }
                else
                {
                    footstepTimer = 0f;
                }
            }
        }

        private void PlayRandomFootstep()
        {
            if (footstepSounds != null && footstepSounds.Length > 0 && footstepAudioSource != null)
            {
                AudioClip randomFootstep = footstepSounds[UnityEngine.Random.Range(0, footstepSounds.Length)];
                if (randomFootstep != null)
                {
                    footstepAudioSource.clip = randomFootstep;
                    footstepAudioSource.volume = footstepVolume;
                    footstepAudioSource.pitch = UnityEngine.Random.Range(0.9f, 1.1f); // Add slight pitch variation
                    footstepAudioSource.Play();
                }
            }
        }

        private void UpdateAllAudio()
        {
            // Update all audio volumes and clips in real-time
            if (slideAudioSource != null)
            {
                slideAudioSource.volume = slideSoundVolume;
                if (slideSound != null && slideAudioSource.clip != slideSound)
                    slideAudioSource.clip = slideSound;
            }

            if (dashAudioSource != null)
            {
                dashAudioSource.volume = dashSoundVolume;
                if (dashSound != null && dashAudioSource.clip != dashSound)
                    dashAudioSource.clip = dashSound;
            }

            if (jumpAudioSource != null)
            {
                jumpAudioSource.volume = jumpSoundVolume;
                if (jumpSound != null && jumpAudioSource.clip != jumpSound)
                    jumpAudioSource.clip = jumpSound;
            }

            if (footstepAudioSource != null)
            {
                footstepAudioSource.volume = footstepVolume;
            }

            if (groundpoundAudioSource != null)
            {
                groundpoundAudioSource.volume = groundpoundSoundVolume;
                if (groundpoundSound != null && groundpoundAudioSource.clip != groundpoundSound)
                    groundpoundAudioSource.clip = groundpoundSound;
            }
        }
    }
}